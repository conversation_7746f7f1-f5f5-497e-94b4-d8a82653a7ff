import { Button } from '@/components/ui/button';
import { ServiceCard } from '@/components/service-card';
import Link from 'next/link';

export default function HomePage() {
  const services = [
    {
      title: "Professional Painting",
      description: "Transform your home with our expert interior and exterior painting services.",
      image: "https://images.pexels.com/photos/1669799/pexels-photo-1669799.jpeg?auto=compress&cs=tinysrgb&w=600"
    },
    {
      title: "Pressure Washing",
      description: "Restore the beauty of your property with our professional pressure washing services.",
      image: "https://images.pexels.com/photos/6474457/pexels-photo-6474457.jpeg?auto=compress&cs=tinysrgb&w=600"
    },
    {
      title: "Wood Repair",
      description: "Expert wood restoration and repair to maintain your property's integrity.",
      image: "https://images.pexels.com/photos/5691659/pexels-photo-5691659.jpeg?auto=compress&cs=tinysrgb&w=600"
    }
  ];

  return (
    <div className="space-y-16">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-b from-blue-50 to-white dark:from-blue-900/20 dark:to-gray-900 py-24">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-8">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-gray-100">
              Transform Your Home with
              <span className="text-blue-600 dark:text-blue-400 block">Graceful Shine</span>
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Professional exterior services that bring out the best in your property. 
              From expert painting to thorough pressure washing and precise wood repair.
            </p>
            <div className="aspect-video max-w-4xl mx-auto bg-gray-200 dark:bg-gray-700 rounded-lg overflow-hidden">
              <img 
                src="https://images.pexels.com/photos/106399/pexels-photo-106399.jpeg?auto=compress&cs=tinysrgb&w=1200"
                alt="Beautiful home exterior"
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Mission Statement */}
      <section className="py-16 bg-white dark:bg-gray-800 transition-colors duration-200">
        <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
            Our Mission
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
            At Graceful Shine, we believe every home deserves to look its absolute best. 
            Our mission is to provide exceptional exterior services that not only enhance 
            your property's appearance but also protect your investment for years to come. 
            With meticulous attention to detail and unwavering commitment to quality, 
            we transform ordinary homes into extraordinary showcases.
          </p>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
              Our Services
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300">
              Comprehensive exterior services to keep your home looking its best
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <ServiceCard key={index} {...service} />
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-blue-600 dark:bg-blue-700 transition-colors duration-200">
        <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Ready to Transform Your Home?
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Get a free, no-obligation estimate for your project today
          </p>
          <Link href="/contact">
            <Button size="lg" variant="secondary" className="bg-white text-blue-600 hover:bg-gray-100 dark:bg-gray-100 dark:text-blue-600 dark:hover:bg-gray-200">
              Get a Free Estimate
            </Button>
          </Link>
        </div>
      </section>
    </div>
  );
}