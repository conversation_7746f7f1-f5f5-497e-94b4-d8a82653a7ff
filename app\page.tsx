import { Button } from '@/components/ui/button';
import { ServiceCard } from '@/components/service-card';
import Link from 'next/link';

export default function HomePage() {
  const services = [
    {
      title: "Professional Painting",
      description: "Transform your home with our expert interior and exterior painting services.",
      image: "https://images.pexels.com/photos/1669799/pexels-photo-1669799.jpeg?auto=compress&cs=tinysrgb&w=600"
    },
    {
      title: "Pressure Washing",
      description: "Restore the beauty of your property with our professional pressure washing services.",
      image: "https://images.pexels.com/photos/6474457/pexels-photo-6474457.jpeg?auto=compress&cs=tinysrgb&w=600"
    },
    {
      title: "Wood Repair",
      description: "Expert wood restoration and repair to maintain your property's integrity.",
      image: "https://images.pexels.com/photos/5691659/pexels-photo-5691659.jpeg?auto=compress&cs=tinysrgb&w=600"
    }
  ];

  return (
    <div className="space-y-16">
      {/* Hero Section */}
      <section
        className="relative bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-blue-900/20 dark:via-gray-900 dark:to-indigo-900/20 py-24 lg:py-32 overflow-hidden"
        aria-label="Hero section"
      >
        {/* Background decoration */}
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="absolute top-0 right-0 w-96 h-96 bg-blue-400/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-indigo-400/10 rounded-full blur-3xl"></div>

        <div className="relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-8 animate-fade-in">
            <div className="space-y-4">
              <h1 className="text-4xl sm:text-5xl lg:text-7xl font-bold text-gray-900 dark:text-gray-100 leading-tight">
                Transform Your Home with
                <span className="gradient-text block mt-2">Graceful Shine</span>
              </h1>
              <div className="w-24 h-1 bg-gradient-to-r from-blue-600 to-indigo-600 mx-auto rounded-full"></div>
            </div>

            <p className="text-xl lg:text-2xl text-gray-600 dark:text-gray-300 max-w-4xl mx-auto leading-relaxed">
              Professional exterior services that bring out the best in your property.
              From expert painting to thorough pressure washing and precise wood repair.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-4">
              <Link href="/contact">
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-8 py-4 text-lg font-semibold shadow-elegant hover:shadow-elegant-lg transition-all duration-300 transform hover:scale-105"
                  aria-label="Get a free estimate for your home exterior project"
                >
                  Get Free Estimate
                </Button>
              </Link>
              <Link href="/services">
                <Button
                  variant="outline"
                  size="lg"
                  className="border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white px-8 py-4 text-lg font-semibold transition-all duration-300"
                  aria-label="View our exterior services"
                >
                  View Our Services
                </Button>
              </Link>
            </div>

            <div className="pt-8 animate-slide-up">
              <div className="aspect-video max-w-5xl mx-auto bg-gray-200 dark:bg-gray-700 rounded-2xl overflow-hidden shadow-elegant-lg">
                <img
                  src="https://images.pexels.com/photos/106399/pexels-photo-106399.jpeg?auto=compress&cs=tinysrgb&w=1200"
                  alt="Beautiful home exterior showcasing professional painting and maintenance"
                  className="w-full h-full object-cover hover:scale-105 transition-transform duration-700"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Mission Statement */}
      <section className="py-20 lg:py-28 bg-gradient-to-b from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 transition-colors duration-200">
        <div className="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6 animate-fade-in">
              <div className="inline-flex items-center justify-center p-2 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                <span className="text-blue-600 dark:text-blue-400 text-sm font-semibold px-3 py-1">OUR MISSION</span>
              </div>
              <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 dark:text-gray-100 leading-tight">
                Excellence in Every
                <span className="gradient-text block">Detail</span>
              </h2>
              <p className="text-xl text-gray-600 dark:text-gray-300 leading-relaxed">
                At Graceful Shine, we believe every home deserves to look its absolute best.
                Our mission is to provide exceptional exterior services that not only enhance
                your property's appearance but also protect your investment for years to come.
              </p>
              <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
                With meticulous attention to detail and unwavering commitment to quality,
                we transform ordinary homes into extraordinary showcases that reflect your pride of ownership.
              </p>
            </div>
            <div className="animate-slide-up">
              <div className="relative">
                <div className="aspect-square bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-2xl overflow-hidden shadow-elegant-lg">
                  <img
                    src="https://images.pexels.com/photos/1396122/pexels-photo-1396122.jpeg?auto=compress&cs=tinysrgb&w=800"
                    alt="Professional team working on home exterior"
                    className="w-full h-full object-cover hover:scale-105 transition-transform duration-700"
                  />
                </div>
                <div className="absolute -bottom-6 -right-6 bg-white dark:bg-gray-800 p-6 rounded-xl shadow-elegant">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600 dark:text-blue-400">100%</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Satisfaction Guaranteed</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-20 lg:py-28 bg-gradient-to-b from-gray-50 to-white dark:from-gray-800 dark:to-gray-900">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-fade-in">
            <div className="inline-flex items-center justify-center p-2 bg-blue-100 dark:bg-blue-900/30 rounded-full mb-4">
              <span className="text-blue-600 dark:text-blue-400 text-sm font-semibold px-3 py-1">OUR EXPERTISE</span>
            </div>
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 dark:text-gray-100 mb-6">
              Premium Services for Your Home
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Comprehensive exterior services designed to enhance, protect, and maintain your property's beauty and value
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10">
            {services.map((service, index) => (
              <div key={index} className="animate-slide-up" style={{ animationDelay: `${index * 0.2}s` }}>
                <ServiceCard {...service} />
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 lg:py-28 bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-700 relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>
        <div className="absolute top-0 right-0 w-96 h-96 bg-white/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-white/5 rounded-full blur-3xl"></div>

        <div className="relative mx-auto max-w-5xl px-4 sm:px-6 lg:px-8 text-center animate-fade-in">
          <div className="space-y-8">
            <h2 className="text-4xl lg:text-6xl font-bold text-white leading-tight">
              Ready to Transform
              <span className="block text-blue-200">Your Home?</span>
            </h2>
            <p className="text-xl lg:text-2xl text-blue-100 max-w-3xl mx-auto leading-relaxed">
              Join hundreds of satisfied homeowners who have trusted us with their exterior transformation.
              Get your free, detailed estimate today.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-4">
              <Link href="/contact">
                <Button size="lg" variant="secondary" className="bg-white text-blue-600 hover:bg-gray-100 dark:bg-gray-100 dark:text-blue-600 dark:hover:bg-gray-200 px-8 py-4 text-lg font-semibold shadow-elegant hover:shadow-elegant-lg transition-all duration-300 transform hover:scale-105">
                  Get Your Free Estimate
                </Button>
              </Link>
              <Link href="/gallery">
                <Button size="lg" variant="outline" className="border-2 border-white text-white hover:bg-white hover:text-blue-600 px-8 py-4 text-lg font-semibold transition-all duration-300">
                  View Our Work
                </Button>
              </Link>
            </div>

            <div className="pt-8 grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
              <div className="space-y-2">
                <div className="text-3xl font-bold text-white">500+</div>
                <div className="text-blue-200">Homes Transformed</div>
              </div>
              <div className="space-y-2">
                <div className="text-3xl font-bold text-white">15+</div>
                <div className="text-blue-200">Years Experience</div>
              </div>
              <div className="space-y-2">
                <div className="text-3xl font-bold text-white">100%</div>
                <div className="text-blue-200">Satisfaction Rate</div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}