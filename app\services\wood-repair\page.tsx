import { Button } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, Hammer, Shield, Clock, Star, TreePine } from 'lucide-react';
import Link from 'next/link';

export default function WoodRepairPage() {
  const services = [
    "Siding repair and replacement",
    "Trim and molding restoration",
    "Deck and fence repairs",
    "Window and door frame repair",
    "Structural wood replacement",
    "Custom millwork matching"
  ];

  const commonIssues = [
    { issue: "Wood Rot", description: "Caused by moisture exposure, leading to structural weakness" },
    { issue: "Insect Damage", description: "Termites, carpenter ants, and other pests can compromise wood integrity" },
    { issue: "Weather Damage", description: "UV exposure, rain, and temperature changes cause cracking and warping" },
    { issue: "Age-Related Wear", description: "Natural aging process requires maintenance and restoration" }
  ];

  return (
    <div className="py-16">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        {/* <PERSON>readcrumbs */}
        <nav className="mb-8 text-sm">
          <ol className="flex items-center space-x-2 text-gray-500 dark:text-gray-400">
            <li><Link href="/" className="hover:text-blue-600 dark:hover:text-blue-400">Home</Link></li>
            <li>/</li>
            <li><Link href="/services" className="hover:text-blue-600 dark:hover:text-blue-400">Services</Link></li>
            <li>/</li>
            <li className="text-gray-900 dark:text-gray-100">Wood Repair</li>
          </ol>
        </nav>

        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 dark:text-gray-100 mb-6">
            Expert Wood Repair & Restoration Services in North Carolina
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
            Restore your home's wood elements to their original beauty and structural integrity. Our skilled craftsmen 
            specialize in matching existing materials and finishes for seamless repairs that last.
          </p>
          <div className="aspect-video max-w-4xl mx-auto bg-gray-200 dark:bg-gray-700 rounded-lg overflow-hidden mb-8">
            <img 
              src="https://images.pexels.com/photos/5691659/pexels-photo-5691659.jpeg?auto=compress&cs=tinysrgb&w=1200"
              alt="Professional wood repair service in North Carolina"
              className="w-full h-full object-cover"
            />
          </div>
        </div>

        <div className="grid lg:grid-cols-3 gap-12">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-12">
            {/* Services Section */}
            <section>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                Our Wood Repair Services
              </h2>
              <div className="grid md:grid-cols-2 gap-6">
                {services.map((service, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <Hammer className="h-6 w-6 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-600 dark:text-gray-300">{service}</span>
                  </div>
                ))}
              </div>
            </section>

            {/* Common Issues */}
            <section>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                Common Wood Problems We Fix
              </h2>
              <div className="space-y-6">
                {commonIssues.map((item, index) => (
                  <Card key={index} className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
                    <CardContent className="p-6">
                      <div className="flex items-start space-x-4">
                        <TreePine className="h-6 w-6 text-green-600 dark:text-green-400 flex-shrink-0 mt-1" />
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                            {item.issue}
                          </h3>
                          <p className="text-gray-600 dark:text-gray-300">{item.description}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </section>

            {/* Process */}
            <section>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                Our Restoration Process
              </h2>
              <Card className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
                <CardContent className="p-6">
                  <div className="space-y-6">
                    <div className="flex items-start space-x-4">
                      <div className="bg-blue-600 dark:bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold flex-shrink-0">1</div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">Assessment & Planning</h3>
                        <p className="text-gray-600 dark:text-gray-300">Thorough inspection to identify all damaged areas and develop a comprehensive repair plan.</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-4">
                      <div className="bg-blue-600 dark:bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold flex-shrink-0">2</div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">Material Matching</h3>
                        <p className="text-gray-600 dark:text-gray-300">Careful selection and preparation of materials to match existing wood species, grain, and finish.</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-4">
                      <div className="bg-blue-600 dark:bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold flex-shrink-0">3</div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">Expert Repair</h3>
                        <p className="text-gray-600 dark:text-gray-300">Skilled craftwork to restore structural integrity and aesthetic appeal.</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-4">
                      <div className="bg-blue-600 dark:bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold flex-shrink-0">4</div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">Finishing & Protection</h3>
                        <p className="text-gray-600 dark:text-gray-300">Application of stains, paints, or sealers to protect and beautify the restored wood.</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </section>

            {/* Preparation for Painting */}
            <section>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                Perfect Foundation for Painting
              </h2>
              <Card className="bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800">
                <CardContent className="p-6">
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    Wood repair is often the crucial first step before painting. Damaged wood won't hold paint properly, 
                    leading to premature failure and costly repainting. We frequently combine our wood repair services with our 
                    <Link href="/services/exterior-painting" className="text-blue-600 dark:text-blue-400 hover:underline mx-1">
                      exterior painting services
                    </Link>
                    to ensure a flawless, long-lasting finish.
                  </p>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">Before Repair</h4>
                      <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                        <li>• Paint peeling and flaking</li>
                        <li>• Visible wood damage</li>
                        <li>• Poor paint adhesion</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">After Repair</h4>
                      <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                        <li>• Smooth, solid surface</li>
                        <li>• Excellent paint adhesion</li>
                        <li>• Long-lasting finish</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </section>

            {/* Service Areas */}
            <section>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                Wood Repair Services Across North Carolina
              </h2>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                We provide professional wood repair and restoration services throughout North Carolina:
              </p>
              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
                {['Charlotte', 'Raleigh', 'Durham', 'Greensboro', 'Winston-Salem', 'Asheville', 'Wilmington', 'Fayetteville'].map((city) => (
                  <Link key={city} href={`/locations/${city.toLowerCase().replace('-', '')}`} 
                        className="text-blue-600 dark:text-blue-400 hover:underline">
                    {city}
                  </Link>
                ))}
              </div>
            </section>
          </div>

          {/* Sidebar */}
          <div className="space-y-8">
            {/* CTA Card */}
            <Card className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
              <CardContent className="p-6 text-center">
                <TreePine className="h-12 w-12 text-green-600 dark:text-green-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  Restore Your Wood
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Expert craftsmanship for lasting repairs
                </p>
                <Link href="/contact">
                  <Button className="w-full bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600">
                    Get Free Assessment
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Features */}
            <Card className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
              <CardHeader>
                <CardTitle className="text-lg text-gray-900 dark:text-gray-100">
                  Our Expertise
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Shield className="h-5 w-5 text-green-600 dark:text-green-400" />
                  <span className="text-gray-600 dark:text-gray-300">Structural Integrity</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Clock className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  <span className="text-gray-600 dark:text-gray-300">Lasting Solutions</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Star className="h-5 w-5 text-yellow-500" />
                  <span className="text-gray-600 dark:text-gray-300">Master Craftsmanship</span>
                </div>
              </CardContent>
            </Card>

            {/* Related Services */}
            <Card className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
              <CardHeader>
                <CardTitle className="text-lg text-gray-900 dark:text-gray-100">
                  Complete Restoration
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Link href="/services/exterior-painting" className="block text-blue-600 dark:text-blue-400 hover:underline">
                  Exterior Painting
                </Link>
                <Link href="/services/pressure-washing" className="block text-blue-600 dark:text-blue-400 hover:underline">
                  Pressure Washing
                </Link>
                <Link href="/services/interior-painting" className="block text-blue-600 dark:text-blue-400 hover:underline">
                  Interior Painting
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}