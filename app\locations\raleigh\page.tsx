import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card';
import { MapPin, Phone, Clock, Star, Home, Users, TreePine } from 'lucide-react';
import Link from 'next/link';

export default function RaleighPage() {
  const neighborhoods = [
    "Downtown Raleigh", "North Hills", "Cary", "Apex", "Wake Forest", 
    "Garner", "Knightdale", "Rolesville", "Wendell", "Zebulon",
    "Holly Springs", "Fuquay-Varina", "Morrisville", "Clayton", "Angier"
  ];

  const services = [
    { name: "Interior Painting", link: "/services/interior-painting" },
    { name: "Exterior Painting", link: "/services/exterior-painting" },
    { name: "Pressure Washing", link: "/services/pressure-washing" },
    { name: "Wood Repair", link: "/services/wood-repair" },
    { name: "Window Cleaning", link: "/services/window-cleaning" }
  ];

  return (
    <div className="py-16">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        {/* Breadcrumbs */}
        <nav className="mb-8 text-sm">
          <ol className="flex items-center space-x-2 text-gray-500 dark:text-gray-400">
            <li><Link href="/" className="hover:text-blue-600 dark:hover:text-blue-400">Home</Link></li>
            <li>/</li>
            <li><Link href="/locations" className="hover:text-blue-600 dark:hover:text-blue-400">Locations</Link></li>
            <li>/</li>
            <li className="text-gray-900 dark:text-gray-100">Raleigh</li>
          </ol>
        </nav>

        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 dark:text-gray-100 mb-6">
            Professional Exterior Services in Raleigh, NC
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
            Serving the Triangle area with premium painting, pressure washing, and wood repair services. 
            From historic downtown properties to modern developments in Cary and Apex, we enhance every home's beauty.
          </p>
          <div className="aspect-video max-w-4xl mx-auto bg-gray-200 dark:bg-gray-700 rounded-lg overflow-hidden mb-8">
            <img 
              src="https://images.pexels.com/photos/271816/pexels-photo-271816.jpeg?auto=compress&cs=tinysrgb&w=1200"
              alt="Raleigh NC home exterior services"
              className="w-full h-full object-cover"
            />
          </div>
        </div>

        <div className="grid lg:grid-cols-3 gap-12">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-12">
            {/* About Raleigh Services */}
            <section>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                Triangle Area Exterior Specialists
              </h2>
              <Card className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
                <CardContent className="p-6">
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    As North Carolina's capital and the heart of the Research Triangle, Raleigh combines historic charm 
                    with modern innovation. Our team understands the unique character of Triangle area homes, from 
                    century-old properties in downtown Raleigh to contemporary developments in the surrounding suburbs.
                  </p>
                  <div className="grid md:grid-cols-2 gap-6 mt-6">
                    <div className="flex items-start space-x-3">
                      <TreePine className="h-6 w-6 text-green-600 dark:text-green-400 flex-shrink-0 mt-1" />
                      <div>
                        <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-1">City of Oaks Heritage</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-300">Specialized care for historic properties and mature tree-lined neighborhoods</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <Users className="h-6 w-6 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-1" />
                      <div>
                        <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-1">Research Triangle Expertise</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-300">Serving professionals and families throughout the Triangle</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </section>

            {/* Service Areas */}
            <section>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                Raleigh-Durham Area Communities We Serve
              </h2>
              <Card className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
                <CardContent className="p-6">
                  <p className="text-gray-600 dark:text-gray-300 mb-6">
                    We proudly serve Raleigh and the greater Triangle area, bringing professional exterior services 
                    to homeowners throughout Wake County and beyond.
                  </p>
                  <div className="grid md:grid-cols-3 gap-4">
                    {neighborhoods.map((neighborhood, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <MapPin className="h-4 w-4 text-blue-600 dark:text-blue-400 flex-shrink-0" />
                        <span className="text-gray-600 dark:text-gray-300">{neighborhood}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </section>

            {/* Triangle Climate */}
            <section>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                Triangle Climate Considerations
              </h2>
              <Card className="bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800">
                <CardContent className="p-6">
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    The Triangle's humid subtropical climate, combined with the area's abundant tree cover, creates 
                    specific challenges for exterior maintenance. We understand how to protect your home from the 
                    region's unique environmental factors.
                  </p>
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">Environmental Factors</h4>
                      <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                        <li>• Heavy tree pollen in spring</li>
                        <li>• High humidity and heat</li>
                        <li>• Occasional severe weather</li>
                        <li>• Organic growth from tree cover</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">Our Approach</h4>
                      <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                        <li>• Thorough surface preparation</li>
                        <li>• Mildew-resistant paint systems</li>
                        <li>• Regular maintenance programs</li>
                        <li>• Eco-friendly cleaning solutions</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </section>

            {/* University Connection */}
            <section>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                Serving the University Community
              </h2>
              <Card className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
                <CardContent className="p-6">
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    With NC State University, Duke, and UNC nearby, the Triangle is home to many faculty, staff, and 
                    graduate students who value quality and reliability. We understand the busy schedules of academic 
                    professionals and offer flexible scheduling to accommodate your needs.
                  </p>
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg mt-4">
                    <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">Academic Community Benefits</h4>
                    <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                      <li>• Flexible scheduling around academic calendars</li>
                      <li>• Special consideration for research and study needs</li>
                      <li>• Understanding of university housing requirements</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </section>

            {/* Services Available */}
            <section>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                Our Raleigh-Durham Services
              </h2>
              <div className="grid md:grid-cols-2 gap-4">
                {services.map((service, index) => (
                  <Link key={index} href={service.link} 
                        className="block p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md transition-shadow">
                    <span className="text-blue-600 dark:text-blue-400 hover:underline font-medium">{service.name}</span>
                  </Link>
                ))}
              </div>
            </section>
          </div>

          {/* Sidebar */}
          <div className="space-y-8">
            {/* Contact Card */}
            <Card className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4 text-center">
                  Serving the Triangle
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <MapPin className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    <span className="text-gray-600 dark:text-gray-300">Raleigh-Durham Area</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Phone className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    <span className="text-gray-600 dark:text-gray-300">(*************</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Clock className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    <span className="text-gray-600 dark:text-gray-300">Mon-Sat: 8AM-6PM</span>
                  </div>
                </div>
                <Link href="/contact" className="block mt-6">
                  <Button className="w-full bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600">
                    Get Free Triangle Estimate
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Why Choose Us */}
            <Card className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
              <CardHeader>
                <CardTitle className="text-lg text-gray-900 dark:text-gray-100">
                  Triangle Area Advantages
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Star className="h-5 w-5 text-yellow-500" />
                  <span className="text-gray-600 dark:text-gray-300">Research Triangle Trusted</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Clock className="h-5 w-5 text-green-600 dark:text-green-400" />
                  <span className="text-gray-600 dark:text-gray-300">Flexible Academic Scheduling</span>
                </div>
                <div className="flex items-center space-x-3">
                  <TreePine className="h-5 w-5 text-green-600 dark:text-green-400" />
                  <span className="text-gray-600 dark:text-gray-300">City of Oaks Specialists</span>
                </div>
              </CardContent>
            </Card>

            {/* Nearby Locations */}
            <Card className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
              <CardHeader>
                <CardTitle className="text-lg text-gray-900 dark:text-gray-100">
                  Nearby Service Areas
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Link href="/locations/durham" className="block text-blue-600 dark:text-blue-400 hover:underline">
                  Durham
                </Link>
                <Link href="/locations/charlotte" className="block text-blue-600 dark:text-blue-400 hover:underline">
                  Charlotte
                </Link>
                <Link href="/locations/greensboro" className="block text-blue-600 dark:text-blue-400 hover:underline">
                  Greensboro
                </Link>
                <Link href="/locations" className="block text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400">
                  View All Locations →
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}