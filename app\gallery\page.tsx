import { GalleryItem } from '@/components/gallery-item';

export default function GalleryPage() {
  const galleryItems = [
    {
      title: "Colonial Home Exterior Refresh",
      beforeImage: "https://images.pexels.com/photos/280222/pexels-photo-280222.jpeg?auto=compress&cs=tinysrgb&w=400",
      afterImage: "https://images.pexels.com/photos/106399/pexels-photo-106399.jpeg?auto=compress&cs=tinysrgb&w=400"
    },
    {
      title: "Deck Restoration Project",
      beforeImage: "https://images.pexels.com/photos/1005456/pexels-photo-1005456.jpeg?auto=compress&cs=tinysrgb&w=400",
      afterImage: "https://images.pexels.com/photos/1396132/pexels-photo-1396132.jpeg?auto=compress&cs=tinysrgb&w=400"
    },
    {
      title: "Siding Pressure Wash",
      beforeImage: "https://images.pexels.com/photos/323775/pexels-photo-323775.jpeg?auto=compress&cs=tinysrgb&w=400",
      afterImage: "https://images.pexels.com/photos/271816/pexels-photo-271816.jpeg?auto=compress&cs=tinysrgb&w=400"
    },
    {
      title: "Front Porch Makeover",
      beforeImage: "https://images.pexels.com/photos/2724749/pexels-photo-2724749.jpeg?auto=compress&cs=tinysrgb&w=400",
      afterImage: "https://images.pexels.com/photos/1396122/pexels-photo-1396122.jpeg?auto=compress&cs=tinysrgb&w=400"
    },
    {
      title: "Trim and Shutters",
      beforeImage: "https://images.pexels.com/photos/2121119/pexels-photo-2121119.jpeg?auto=compress&cs=tinysrgb&w=400",
      afterImage: "https://images.pexels.com/photos/1396909/pexels-photo-1396909.jpeg?auto=compress&cs=tinysrgb&w=400"
    },
    {
      title: "Wood Fence Restoration",
      beforeImage: "https://images.pexels.com/photos/1108701/pexels-photo-1108701.jpeg?auto=compress&cs=tinysrgb&w=400",
      afterImage: "https://images.pexels.com/photos/2724748/pexels-photo-2724748.jpeg?auto=compress&cs=tinysrgb&w=400"
    }
  ];

  return (
    <div className="py-16">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            Our Work Gallery
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            See the dramatic transformations we've achieved for homeowners across North Carolina. 
            Each project showcases our commitment to quality, attention to detail, and the 
            graceful shine that sets us apart.
          </p>
        </div>
        
        <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {galleryItems.map((item, index) => (
            <GalleryItem key={index} {...item} />
          ))}
        </div>
        
        <div className="mt-12 text-center">
          <p className="text-gray-600 dark:text-gray-300">
            Ready to see your home transformed? 
            <span className="text-blue-600 dark:text-blue-400 font-semibold ml-1">
              Contact us for your free estimate today!
            </span>
          </p>
        </div>
      </div>
    </div>
  );
}