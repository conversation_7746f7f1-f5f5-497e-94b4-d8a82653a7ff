import Link from 'next/link';
import { Heart, Sparkles } from 'lucide-react';

export function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gradient-to-r from-gray-50 to-blue-50/30 dark:from-gray-800 dark:to-blue-900/20 border-t border-gray-200 dark:border-gray-700 transition-colors duration-200">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center space-y-6">
          {/* Logo */}
          <div className="flex justify-center items-center space-x-2">
            <Sparkles className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            <span className="text-2xl font-bold gradient-text">Graceful Shine</span>
            <Sparkles className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>

          {/* Tagline */}
          <p className="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
            Transforming homes with professional exterior services across North Carolina
          </p>

          {/* Quick Links */}
          <div className="flex flex-wrap justify-center gap-6 text-sm">
            <Link href="/services" className="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
              Services
            </Link>
            <Link href="/locations" className="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
              Locations
            </Link>
            <Link href="/gallery" className="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
              Gallery
            </Link>
            <Link href="/contact" className="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
              Contact
            </Link>
          </div>

          {/* Copyright */}
          <div className="pt-6 border-t border-gray-200 dark:border-gray-700">
            <p className="text-gray-500 dark:text-gray-500 flex items-center justify-center space-x-1">
              <span>© {currentYear} Graceful Shine. Made with</span>
              <Heart className="h-4 w-4 text-red-500 fill-current" />
              <span>in North Carolina</span>
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}