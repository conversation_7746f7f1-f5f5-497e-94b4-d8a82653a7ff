import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, Droplets, Shield, Clock, Star, Zap } from 'lucide-react';
import Link from 'next/link';

export default function PressureWashingPage() {
  const services = [
    "House siding and exterior walls",
    "Driveways and walkways",
    "Decks and patios",
    "Fences and outdoor structures",
    "Roofs and gutters",
    "Commercial properties"
  ];

  const benefits = [
    "Remove dirt, grime, and mildew buildup",
    "Prevent costly repairs and replacements",
    "Improve curb appeal instantly",
    "Prepare surfaces for painting",
    "Eco-friendly cleaning solutions"
  ];

  return (
    <div className="py-16">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        {/* Breadcrumbs */}
        <nav className="mb-8 text-sm">
          <ol className="flex items-center space-x-2 text-gray-500 dark:text-gray-400">
            <li><Link href="/" className="hover:text-blue-600 dark:hover:text-blue-400">Home</Link></li>
            <li>/</li>
            <li><Link href="/services" className="hover:text-blue-600 dark:hover:text-blue-400">Services</Link></li>
            <li>/</li>
            <li className="text-gray-900 dark:text-gray-100">Pressure Washing</li>
          </ol>
        </nav>

        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 dark:text-gray-100 mb-6">
            Professional Pressure Washing Services in North Carolina
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
            Restore your property's beauty with our expert pressure washing services. Safe, effective, and environmentally 
            friendly cleaning that removes years of buildup and prepares surfaces for maintenance or painting.
          </p>
          <div className="aspect-video max-w-4xl mx-auto bg-gray-200 dark:bg-gray-700 rounded-lg overflow-hidden mb-8">
            <img 
              src="https://images.pexels.com/photos/6474457/pexels-photo-6474457.jpeg?auto=compress&cs=tinysrgb&w=1200"
              alt="Professional pressure washing service in North Carolina"
              className="w-full h-full object-cover"
            />
          </div>
        </div>

        <div className="grid lg:grid-cols-3 gap-12">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-12">
            {/* Services Section */}
            <section>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                What We Clean
              </h2>
              <div className="grid md:grid-cols-2 gap-6">
                {services.map((service, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <Droplets className="h-6 w-6 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-600 dark:text-gray-300">{service}</span>
                  </div>
                ))}
              </div>
            </section>

            {/* Benefits Section */}
            <section>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                Benefits of Professional Pressure Washing
              </h2>
              <div className="grid md:grid-cols-2 gap-6">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-600 dark:text-gray-300">{benefit}</span>
                  </div>
                ))}
              </div>
            </section>

            {/* Why Professional */}
            <section>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                Why Choose Professional Pressure Washing?
              </h2>
              <Card className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
                <CardContent className="p-6">
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    While DIY pressure washing might seem simple, improper technique can damage surfaces, force water into 
                    unwanted areas, or fail to achieve the deep clean your property needs. Our professional team has the 
                    experience and equipment to clean effectively while protecting your investment.
                  </p>
                  <div className="grid md:grid-cols-2 gap-4 mt-6">
                    <div className="space-y-3">
                      <h4 className="font-semibold text-gray-900 dark:text-gray-100">Professional Equipment</h4>
                      <ul className="space-y-1 text-sm text-gray-600 dark:text-gray-300">
                        <li>• Commercial-grade pressure washers</li>
                        <li>• Variable pressure settings</li>
                        <li>• Specialized cleaning attachments</li>
                      </ul>
                    </div>
                    <div className="space-y-3">
                      <h4 className="font-semibold text-gray-900 dark:text-gray-100">Expert Technique</h4>
                      <ul className="space-y-1 text-sm text-gray-600 dark:text-gray-300">
                        <li>• Surface-appropriate pressure levels</li>
                        <li>• Proper cleaning angles and patterns</li>
                        <li>• Safe chemical application when needed</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </section>

            {/* Before Painting */}
            <section>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                Perfect Preparation for Painting
              </h2>
              <Card className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
                <CardContent className="p-6">
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    Planning to paint your home's exterior? Pressure washing is an essential first step that ensures 
                    optimal paint adhesion and longevity. We often combine our pressure washing services with our 
                    <Link href="/services/exterior-painting" className="text-blue-600 dark:text-blue-400 hover:underline mx-1">
                      exterior painting services
                    </Link>
                    for a complete transformation.
                  </p>
                  <Link href="/contact">
                    <Button className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600">
                      Get Combined Service Quote
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            </section>

            {/* Service Areas */}
            <section>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                Pressure Washing Services Across North Carolina
              </h2>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                We provide professional pressure washing services throughout North Carolina:
              </p>
              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
                {['Charlotte', 'Raleigh', 'Durham', 'Greensboro', 'Winston-Salem', 'Asheville', 'Wilmington', 'Fayetteville'].map((city) => (
                  <Link key={city} href={`/locations/${city.toLowerCase().replace('-', '')}`} 
                        className="text-blue-600 dark:text-blue-400 hover:underline">
                    {city}
                  </Link>
                ))}
              </div>
            </section>
          </div>

          {/* Sidebar */}
          <div className="space-y-8">
            {/* CTA Card */}
            <Card className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
              <CardContent className="p-6 text-center">
                <Zap className="h-12 w-12 text-blue-600 dark:text-blue-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  Instant Transformation
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  See immediate results with professional pressure washing
                </p>
                <Link href="/contact">
                  <Button className="w-full bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600">
                    Get Free Estimate
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Features */}
            <Card className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
              <CardHeader>
                <CardTitle className="text-lg text-gray-900 dark:text-gray-100">
                  Our Promise
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Shield className="h-5 w-5 text-green-600 dark:text-green-400" />
                  <span className="text-gray-600 dark:text-gray-300">Safe for All Surfaces</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Clock className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  <span className="text-gray-600 dark:text-gray-300">Quick & Efficient</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Star className="h-5 w-5 text-yellow-500" />
                  <span className="text-gray-600 dark:text-gray-300">100% Satisfaction Guarantee</span>
                </div>
              </CardContent>
            </Card>

            {/* Related Services */}
            <Card className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
              <CardHeader>
                <CardTitle className="text-lg text-gray-900 dark:text-gray-100">
                  Complete Property Care
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Link href="/services/exterior-painting" className="block text-blue-600 dark:text-blue-400 hover:underline">
                  Exterior Painting
                </Link>
                <Link href="/services/wood-repair" className="block text-blue-600 dark:text-blue-400 hover:underline">
                  Wood Repair & Restoration
                </Link>
                <Link href="/services/window-cleaning" className="block text-blue-600 dark:text-blue-400 hover:underline">
                  Window Cleaning
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}