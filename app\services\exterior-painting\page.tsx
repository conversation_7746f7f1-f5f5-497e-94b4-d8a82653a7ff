import { Button } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, Home, Shield, Clock, Star, Sun } from 'lucide-react';
import Link from 'next/link';

export default function ExteriorPaintingPage() {
  const benefits = [
    "Protect your home from weather damage",
    "Increase curb appeal and property value",
    "Prevent costly structural repairs",
    "Weather-resistant paint systems",
    "Professional color matching and consultation"
  ];

  const process = [
    { step: 1, title: "Property Assessment", description: "Thorough inspection of exterior surfaces and condition evaluation" },
    { step: 2, title: "Power Washing & Prep", description: "Deep cleaning and surface preparation for optimal paint adhesion" },
    { step: 3, title: "Priming & Painting", description: "Multi-coat application with weather-resistant premium paints" },
    { step: 4, title: "Quality Assurance", description: "Final inspection and touch-ups to ensure perfect results" }
  ];

  return (
    <div className="py-16">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        {/* Breadcrumbs */}
        <nav className="mb-8 text-sm">
          <ol className="flex items-center space-x-2 text-gray-500 dark:text-gray-400">
            <li><Link href="/" className="hover:text-blue-600 dark:hover:text-blue-400">Home</Link></li>
            <li>/</li>
            <li><Link href="/services" className="hover:text-blue-600 dark:hover:text-blue-400">Services</Link></li>
            <li>/</li>
            <li className="text-gray-900 dark:text-gray-100">Exterior Painting</li>
          </ol>
        </nav>

        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 dark:text-gray-100 mb-6">
            Expert Exterior Painting Services in North Carolina
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
            Protect and beautify your home's exterior with our professional painting services. 
            We use weather-resistant paints and proven techniques to deliver lasting results that withstand North Carolina's climate.
          </p>
          <div className="aspect-video max-w-4xl mx-auto bg-gray-200 dark:bg-gray-700 rounded-lg overflow-hidden mb-8">
            <img 
              src="https://images.pexels.com/photos/106399/pexels-photo-106399.jpeg?auto=compress&cs=tinysrgb&w=1200"
              alt="Professional exterior painting service in North Carolina"
              className="w-full h-full object-cover"
            />
          </div>
        </div>

        <div className="grid lg:grid-cols-3 gap-12">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-12">
            {/* Benefits Section */}
            <section>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                Benefits of Professional Exterior Painting
              </h2>
              <div className="grid md:grid-cols-2 gap-6">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-600 dark:text-gray-300">{benefit}</span>
                  </div>
                ))}
              </div>
            </section>

            {/* Climate Considerations */}
            <section>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                North Carolina Climate Considerations
              </h2>
              <Card className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
                <CardContent className="p-6">
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    North Carolina's diverse climate presents unique challenges for exterior painting. From the humid coastal regions 
                    to the mountain areas with temperature fluctuations, we understand how to select and apply paints that will 
                    perform optimally in your specific location.
                  </p>
                  <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                    <li>• High humidity resistance for coastal areas like <Link href="/locations/wilmington" className="text-blue-600 dark:text-blue-400 hover:underline">Wilmington</Link></li>
                    <li>• Temperature-resistant formulations for mountain regions like <Link href="/locations/asheville" className="text-blue-600 dark:text-blue-400 hover:underline">Asheville</Link></li>
                    <li>• UV protection for sunny areas throughout the state</li>
                    <li>• Mildew and moisture resistance for all climates</li>
                  </ul>
                </CardContent>
              </Card>
            </section>

            {/* Process Section */}
            <section>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                Our Exterior Painting Process
              </h2>
              <div className="space-y-6">
                {process.map((item, index) => (
                  <Card key={index} className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
                    <CardContent className="p-6">
                      <div className="flex items-start space-x-4">
                        <div className="bg-blue-600 dark:bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold flex-shrink-0">
                          {item.step}
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                            {item.title}
                          </h3>
                          <p className="text-gray-600 dark:text-gray-300">{item.description}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </section>

            {/* Service Areas */}
            <section>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                Exterior Painting Services Across North Carolina
              </h2>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                We provide professional exterior painting services throughout North Carolina:
              </p>
              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
                {['Charlotte', 'Raleigh', 'Durham', 'Greensboro', 'Winston-Salem', 'Asheville', 'Wilmington', 'Fayetteville'].map((city) => (
                  <Link key={city} href={`/locations/${city.toLowerCase().replace('-', '')}`} 
                        className="text-blue-600 dark:text-blue-400 hover:underline">
                    {city}
                  </Link>
                ))}
              </div>
            </section>
          </div>

          {/* Sidebar */}
          <div className="space-y-8">
            {/* CTA Card */}
            <Card className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
              <CardContent className="p-6 text-center">
                <Home className="h-12 w-12 text-blue-600 dark:text-blue-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  Protect Your Investment
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Get your free exterior painting estimate
                </p>
                <Link href="/contact">
                  <Button className="w-full bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600">
                    Get Free Estimate
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Features */}
            <Card className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
              <CardHeader>
                <CardTitle className="text-lg text-gray-900 dark:text-gray-100">
                  Our Guarantee
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Shield className="h-5 w-5 text-green-600 dark:text-green-400" />
                  <span className="text-gray-600 dark:text-gray-300">5-Year Paint Warranty</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Sun className="h-5 w-5 text-yellow-500" />
                  <span className="text-gray-600 dark:text-gray-300">Weather-Resistant Finish</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Star className="h-5 w-5 text-yellow-500" />
                  <span className="text-gray-600 dark:text-gray-300">100% Satisfaction Guarantee</span>
                </div>
              </CardContent>
            </Card>

            {/* Related Services */}
            <Card className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
              <CardHeader>
                <CardTitle className="text-lg text-gray-900 dark:text-gray-100">
                  Complete Exterior Care
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Link href="/services/pressure-washing" className="block text-blue-600 dark:text-blue-400 hover:underline">
                  Pressure Washing
                </Link>
                <Link href="/services/wood-repair" className="block text-blue-600 dark:text-blue-400 hover:underline">
                  Wood Repair & Restoration
                </Link>
                <Link href="/services/interior-painting" className="block text-blue-600 dark:text-blue-400 hover:underline">
                  Interior Painting
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}