import { Card, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { MapPin } from 'lucide-react';

interface LocationCardProps {
  city: string;
  state: string;
  description: string;
}

export function LocationCard({ city, state, description }: LocationCardProps) {
  return (
    <Card className="hover:shadow-lg transition-shadow duration-200 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
      <CardHeader>
        <CardTitle className="text-lg text-gray-900 dark:text-gray-100 flex items-center gap-2">
          <MapPin className="h-5 w-5 text-blue-600 dark:text-blue-400" />
          {city}, {state}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-gray-600 dark:text-gray-300">{description}</p>
      </CardContent>
    </Card>
  );
}