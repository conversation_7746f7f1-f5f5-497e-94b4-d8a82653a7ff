import Link from 'next/link';
import { LocationCard } from '@/components/location-card';

export default function LocationsPage() {
  const locations = [
    {
      city: "Charlotte",
      state: "NC",
      description: "Serving the Queen City and surrounding neighborhoods with premium exterior services."
    },
    {
      city: "Raleigh",
      state: "NC",
      description: "Professional painting and pressure washing services throughout the Triangle area."
    },
    {
      city: "Durham",
      state: "NC",
      description: "Expert wood repair and exterior maintenance for Durham homes and businesses."
    },
    {
      city: "Greensboro",
      state: "NC",
      description: "Quality exterior services for the Triad region, focusing on lasting results."
    },
    {
      city: "Winston-Salem",
      state: "NC",
      description: "Comprehensive exterior care for historic and modern properties alike."
    },
    {
      city: "Asheville",
      state: "NC",
      description: "Specialized services for mountain properties, including weather protection solutions."
    },
    {
      city: "Wilmington",
      state: "NC",
      description: "Coastal property specialists with expertise in salt air and humidity challenges."
    },
    {
      city: "Fayetteville",
      state: "NC",
      description: "Reliable exterior services for military families and local communities."
    }
  ];

  return (
    <div className="py-16">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            Our Service Areas
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            We proudly serve communities across North Carolina, bringing professional 
            exterior services to homeowners throughout the state. Our experienced team 
            travels to your location, ensuring consistent quality no matter where you're located.
          </p>
        </div>
        
        <div className="grid sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {locations.map((location, index) => (
            <Link key={index} href={`/locations/${location.city.toLowerCase().replace('-', '')}`}>
              <LocationCard {...location} />
            </Link>
          ))}
        </div>
        
        <div className="mt-12 text-center">
          <p className="text-gray-600 dark:text-gray-300">
            Don't see your city listed? <span className="text-blue-600 dark:text-blue-400 font-semibold">Contact us</span> - 
            we may still be able to serve your area!
          </p>
        </div>
      </div>
    </div>
  );
}