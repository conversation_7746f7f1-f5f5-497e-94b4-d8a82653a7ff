import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON><PERSON><PERSON>, Palette, Shield, Clock, Star } from 'lucide-react';
import Link from 'next/link';

export default function InteriorPaintingPage() {
  const benefits = [
    "Increase home value by up to 10%",
    "Transform living spaces instantly",
    "Protect walls from wear and damage",
    "Improve indoor air quality with low-VOC paints",
    "Create the perfect ambiance for your lifestyle"
  ];

  const process = [
    { step: 1, title: "Free Consultation", description: "We assess your space and discuss color options and timeline" },
    { step: 2, title: "Surface Preparation", description: "Thorough cleaning, patching, and priming for perfect results" },
    { step: 3, title: "Professional Application", description: "Expert painting with premium materials and techniques" },
    { step: 4, title: "Final Inspection", description: "Quality check and walkthrough to ensure your satisfaction" }
  ];

  const faqs = [
    {
      question: "How long does interior painting take?",
      answer: "Most interior painting projects take 2-4 days depending on the size and complexity. We'll provide a detailed timeline during your consultation."
    },
    {
      question: "Do you move furniture?",
      answer: "Yes, we carefully move and protect your furniture. We recommend removing small valuables and personal items beforehand."
    },
    {
      question: "What type of paint do you use?",
      answer: "We use premium, low-VOC paints from trusted brands like Sherwin-Williams and Benjamin Moore for durability and safety."
    }
  ];

  return (
    <div className="py-16">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        {/* Breadcrumbs */}
        <nav className="mb-8 text-sm">
          <ol className="flex items-center space-x-2 text-gray-500 dark:text-gray-400">
            <li><Link href="/" className="hover:text-blue-600 dark:hover:text-blue-400">Home</Link></li>
            <li>/</li>
            <li><Link href="/services" className="hover:text-blue-600 dark:hover:text-blue-400">Services</Link></li>
            <li>/</li>
            <li className="text-gray-900 dark:text-gray-100">Interior Painting</li>
          </ol>
        </nav>

        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 dark:text-gray-100 mb-6">
            Professional Interior Painting Services in North Carolina
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
            Transform your home's interior with our expert painting services. From color consultation to flawless application, 
            we deliver stunning results that enhance your living space and increase your property value.
          </p>
          <div className="aspect-video max-w-4xl mx-auto bg-gray-200 dark:bg-gray-700 rounded-lg overflow-hidden mb-8">
            <img 
              src="https://images.pexels.com/photos/1669799/pexels-photo-1669799.jpeg?auto=compress&cs=tinysrgb&w=1200"
              alt="Professional interior painting service in North Carolina"
              className="w-full h-full object-cover"
            />
          </div>
        </div>

        <div className="grid lg:grid-cols-3 gap-12">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-12">
            {/* Benefits Section */}
            <section>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                Why Choose Our Interior Painting Services?
              </h2>
              <div className="grid md:grid-cols-2 gap-6">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-600 dark:text-gray-300">{benefit}</span>
                  </div>
                ))}
              </div>
            </section>

            {/* Process Section */}
            <section>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                Our Interior Painting Process
              </h2>
              <div className="space-y-6">
                {process.map((item, index) => (
                  <Card key={index} className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
                    <CardContent className="p-6">
                      <div className="flex items-start space-x-4">
                        <div className="bg-blue-600 dark:bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold flex-shrink-0">
                          {item.step}
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                            {item.title}
                          </h3>
                          <p className="text-gray-600 dark:text-gray-300">{item.description}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </section>

            {/* Service Areas */}
            <section>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                Interior Painting Services Across North Carolina
              </h2>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                We provide professional interior painting services throughout North Carolina, including:
              </p>
              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
                {['Charlotte', 'Raleigh', 'Durham', 'Greensboro', 'Winston-Salem', 'Asheville', 'Wilmington', 'Fayetteville'].map((city) => (
                  <Link key={city} href={`/locations/${city.toLowerCase().replace('-', '')}`} 
                        className="text-blue-600 dark:text-blue-400 hover:underline">
                    {city}
                  </Link>
                ))}
              </div>
            </section>

            {/* FAQs */}
            <section>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                Frequently Asked Questions
              </h2>
              <div className="space-y-6">
                {faqs.map((faq, index) => (
                  <Card key={index} className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
                    <CardHeader>
                      <CardTitle className="text-lg text-gray-900 dark:text-gray-100">
                        {faq.question}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-gray-600 dark:text-gray-300">{faq.answer}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </section>
          </div>

          {/* Sidebar */}
          <div className="space-y-8">
            {/* CTA Card */}
            <Card className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
              <CardContent className="p-6 text-center">
                <Palette className="h-12 w-12 text-blue-600 dark:text-blue-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  Ready to Transform Your Space?
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Get your free color consultation and estimate today
                </p>
                <Link href="/contact">
                  <Button className="w-full bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600">
                    Get Free Estimate
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Features */}
            <Card className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
              <CardHeader>
                <CardTitle className="text-lg text-gray-900 dark:text-gray-100">
                  Why Graceful Shine?
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Shield className="h-5 w-5 text-green-600 dark:text-green-400" />
                  <span className="text-gray-600 dark:text-gray-300">Licensed & Insured</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Clock className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  <span className="text-gray-600 dark:text-gray-300">On-Time Completion</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Star className="h-5 w-5 text-yellow-500" />
                  <span className="text-gray-600 dark:text-gray-300">100% Satisfaction Guarantee</span>
                </div>
              </CardContent>
            </Card>

            {/* Related Services */}
            <Card className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
              <CardHeader>
                <CardTitle className="text-lg text-gray-900 dark:text-gray-100">
                  Related Services
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Link href="/services/exterior-painting" className="block text-blue-600 dark:text-blue-400 hover:underline">
                  Exterior Painting
                </Link>
                <Link href="/services/pressure-washing" className="block text-blue-600 dark:text-blue-400 hover:underline">
                  Pressure Washing
                </Link>
                <Link href="/services/wood-repair" className="block text-blue-600 dark:text-blue-400 hover:underline">
                  Wood Repair & Restoration
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}