import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';

interface ServiceDetailsCardProps {
  title: string;
  description: string;
  features: string[];
  isComingSoon?: boolean;
}

export function ServiceDetailsCard({ title, description, features, isComingSoon = false }: ServiceDetailsCardProps) {
  return (
    <Card className={`hover:shadow-lg transition-shadow duration-200 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 ${isComingSoon ? 'opacity-75' : ''}`}>
      <CardHeader>
        <CardTitle className="text-xl text-gray-900 dark:text-gray-100 flex items-center gap-2">
          {title}
          {isComingSoon && (
            <span className="text-sm bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 px-2 py-1 rounded-full">
              Coming Soon!
            </span>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-gray-600 dark:text-gray-300">{description}</p>
        {!isComingSoon && (
          <ul className="space-y-2">
            {features.map((feature, index) => (
              <li key={index} className="flex items-start">
                <span className="text-blue-600 dark:text-blue-400 mr-2">•</span>
                <span className="text-gray-600 dark:text-gray-300">{feature}</span>
              </li>
            ))}
          </ul>
        )}
      </CardContent>
    </Card>
  );
}