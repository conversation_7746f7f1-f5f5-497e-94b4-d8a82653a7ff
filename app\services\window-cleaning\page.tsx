import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, Eye, Shield, Clock, Star, Sparkles } from 'lucide-react';
import Link from 'next/link';

export default function WindowCleaningPage() {
  const services = [
    "Interior and exterior window cleaning",
    "Screen cleaning and repair",
    "Sill and frame detailing",
    "High-rise window cleaning",
    "Post-construction cleanup",
    "Regular maintenance programs"
  ];

  const benefits = [
    "Crystal clear, streak-free results",
    "Enhanced natural light in your home",
    "Improved curb appeal",
    "Extended window lifespan",
    "Professional-grade equipment and techniques"
  ];

  return (
    <div className="py-16">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        {/* Breadcrumbs */}
        <nav className="mb-8 text-sm">
          <ol className="flex items-center space-x-2 text-gray-500 dark:text-gray-400">
            <li><Link href="/" className="hover:text-blue-600 dark:hover:text-blue-400">Home</Link></li>
            <li>/</li>
            <li><Link href="/services" className="hover:text-blue-600 dark:hover:text-blue-400">Services</Link></li>
            <li>/</li>
            <li className="text-gray-900 dark:text-gray-100">Window Cleaning</li>
          </ol>
        </nav>

        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 dark:text-gray-100 mb-6">
            Professional Window Cleaning Services in North Carolina
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
            Let more light into your home with our professional window cleaning services. We deliver crystal-clear, 
            streak-free results that enhance your view and improve your home's appearance inside and out.
          </p>
          <div className="aspect-video max-w-4xl mx-auto bg-gray-200 dark:bg-gray-700 rounded-lg overflow-hidden mb-8">
            <img 
              src="https://images.pexels.com/photos/2724749/pexels-photo-2724749.jpeg?auto=compress&cs=tinysrgb&w=1200"
              alt="Professional window cleaning service in North Carolina"
              className="w-full h-full object-cover"
            />
          </div>
        </div>

        <div className="grid lg:grid-cols-3 gap-12">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-12">
            {/* Services Section */}
            <section>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                Our Window Cleaning Services
              </h2>
              <div className="grid md:grid-cols-2 gap-6">
                {services.map((service, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <Sparkles className="h-6 w-6 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-600 dark:text-gray-300">{service}</span>
                  </div>
                ))}
              </div>
            </section>

            {/* Benefits Section */}
            <section>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                Benefits of Professional Window Cleaning
              </h2>
              <div className="grid md:grid-cols-2 gap-6">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-600 dark:text-gray-300">{benefit}</span>
                  </div>
                ))}
              </div>
            </section>

            {/* Why Professional */}
            <section>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                The Professional Difference
              </h2>
              <Card className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
                <CardContent className="p-6">
                  <p className="text-gray-600 dark:text-gray-300 mb-6">
                    While cleaning windows might seem straightforward, achieving truly streak-free, crystal-clear results 
                    requires professional techniques, tools, and experience. Our team delivers results that DIY methods 
                    simply can't match.
                  </p>
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">Professional Tools</h4>
                      <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                        <li>• Professional-grade squeegees</li>
                        <li>• Specialized cleaning solutions</li>
                        <li>• Water-fed pole systems</li>
                        <li>• Safety equipment for high windows</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">Expert Techniques</h4>
                      <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                        <li>• Streak-free cleaning methods</li>
                        <li>• Proper solution application</li>
                        <li>• Frame and sill detailing</li>
                        <li>• Screen cleaning and maintenance</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </section>

            {/* Maintenance Programs */}
            <section>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                Regular Maintenance Programs
              </h2>
              <Card className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
                <CardContent className="p-6">
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    Keep your windows looking their best year-round with our maintenance programs. Regular cleaning 
                    prevents buildup that becomes harder to remove over time and ensures your home always looks its best.
                  </p>
                  <div className="grid md:grid-cols-3 gap-4 mt-6">
                    <div className="text-center">
                      <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">Quarterly</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-300">Perfect for most homes</p>
                    </div>
                    <div className="text-center">
                      <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">Bi-Annual</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-300">Spring and fall cleaning</p>
                    </div>
                    <div className="text-center">
                      <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">Monthly</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-300">For commercial properties</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </section>

            {/* Service Areas */}
            <section>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                Window Cleaning Services Across North Carolina
              </h2>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                We provide professional window cleaning services throughout North Carolina:
              </p>
              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
                {['Charlotte', 'Raleigh', 'Durham', 'Greensboro', 'Winston-Salem', 'Asheville', 'Wilmington', 'Fayetteville'].map((city) => (
                  <Link key={city} href={`/locations/${city.toLowerCase().replace('-', '')}`} 
                        className="text-blue-600 dark:text-blue-400 hover:underline">
                    {city}
                  </Link>
                ))}
              </div>
            </section>
          </div>

          {/* Sidebar */}
          <div className="space-y-8">
            {/* CTA Card */}
            <Card className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
              <CardContent className="p-6 text-center">
                <Eye className="h-12 w-12 text-blue-600 dark:text-blue-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  Crystal Clear Views
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Professional window cleaning for sparkling results
                </p>
                <Link href="/contact">
                  <Button className="w-full bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600">
                    Schedule Cleaning
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Features */}
            <Card className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
              <CardHeader>
                <CardTitle className="text-lg text-gray-900 dark:text-gray-100">
                  Our Guarantee
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Shield className="h-5 w-5 text-green-600 dark:text-green-400" />
                  <span className="text-gray-600 dark:text-gray-300">Fully Insured</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Clock className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  <span className="text-gray-600 dark:text-gray-300">Reliable Scheduling</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Star className="h-5 w-5 text-yellow-500" />
                  <span className="text-gray-600 dark:text-gray-300">Streak-Free Guarantee</span>
                </div>
              </CardContent>
            </Card>

            {/* Related Services */}
            <Card className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
              <CardHeader>
                <CardTitle className="text-lg text-gray-900 dark:text-gray-100">
                  Complete Property Care
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Link href="/services/pressure-washing" className="block text-blue-600 dark:text-blue-400 hover:underline">
                  Pressure Washing
                </Link>
                <Link href="/services/exterior-painting" className="block text-blue-600 dark:text-blue-400 hover:underline">
                  Exterior Painting
                </Link>
                <Link href="/services/interior-painting" className="block text-blue-600 dark:text-blue-400 hover:underline">
                  Interior Painting
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}