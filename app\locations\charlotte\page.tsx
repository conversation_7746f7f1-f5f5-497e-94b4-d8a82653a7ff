import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { MapPin, Phone, Clock, Star, Home, Users } from 'lucide-react';
import Link from 'next/link';

export default function CharlottePage() {
  const neighborhoods = [
    "Uptown Charlotte", "South End", "NoDa", "Plaza Midwood", "Dilworth", 
    "Myers Park", "Ballantyne", "SouthPark", "University City", "Cornelius",
    "Huntersville", "Matthews", "Mint Hill", "Pineville", "Waxhaw"
  ];

  const services = [
    { name: "Interior Painting", link: "/services/interior-painting" },
    { name: "Exterior Painting", link: "/services/exterior-painting" },
    { name: "Pressure Washing", link: "/services/pressure-washing" },
    { name: "Wood Repair", link: "/services/wood-repair" },
    { name: "Window Cleaning", link: "/services/window-cleaning" }
  ];

  return (
    <div className="py-16">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        {/* Breadcrumbs */}
        <nav className="mb-8 text-sm">
          <ol className="flex items-center space-x-2 text-gray-500 dark:text-gray-400">
            <li><Link href="/" className="hover:text-blue-600 dark:hover:text-blue-400">Home</Link></li>
            <li>/</li>
            <li><Link href="/locations" className="hover:text-blue-600 dark:hover:text-blue-400">Locations</Link></li>
            <li>/</li>
            <li className="text-gray-900 dark:text-gray-100">Charlotte</li>
          </ol>
        </nav>

        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 dark:text-gray-100 mb-6">
            Professional Exterior Services in Charlotte, NC
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
            Serving the Queen City and surrounding areas with premium painting, pressure washing, and wood repair services. 
            From Uptown high-rises to suburban homes in Ballantyne, we bring graceful shine to every property.
          </p>
          <div className="aspect-video max-w-4xl mx-auto bg-gray-200 dark:bg-gray-700 rounded-lg overflow-hidden mb-8">
            <img 
              src="https://images.pexels.com/photos/280222/pexels-photo-280222.jpeg?auto=compress&cs=tinysrgb&w=1200"
              alt="Charlotte NC home exterior services"
              className="w-full h-full object-cover"
            />
          </div>
        </div>

        <div className="grid lg:grid-cols-3 gap-12">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-12">
            {/* About Charlotte Services */}
            <section>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                Why Charlotte Homeowners Choose Graceful Shine
              </h2>
              <Card className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
                <CardContent className="p-6">
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    Charlotte's rapid growth and diverse architecture require specialized expertise. From historic homes in 
                    Dilworth to modern developments in South End, we understand the unique needs of Charlotte properties. 
                    Our team is familiar with local building styles, climate considerations, and HOA requirements throughout 
                    the greater Charlotte metropolitan area.
                  </p>
                  <div className="grid md:grid-cols-2 gap-6 mt-6">
                    <div className="flex items-start space-x-3">
                      <Home className="h-6 w-6 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-1" />
                      <div>
                        <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-1">Local Expertise</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-300">Deep knowledge of Charlotte's diverse neighborhoods and architectural styles</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <Users className="h-6 w-6 text-green-600 dark:text-green-400 flex-shrink-0 mt-1" />
                      <div>
                        <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-1">Community Focused</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-300">Proud to serve our neighbors throughout the Queen City</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </section>

            {/* Service Areas */}
            <section>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                Charlotte Area Neighborhoods We Serve
              </h2>
              <Card className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
                <CardContent className="p-6">
                  <p className="text-gray-600 dark:text-gray-300 mb-6">
                    We proudly serve Charlotte and the surrounding communities, bringing professional exterior services 
                    to homeowners throughout Mecklenburg County and beyond.
                  </p>
                  <div className="grid md:grid-cols-3 gap-4">
                    {neighborhoods.map((neighborhood, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <MapPin className="h-4 w-4 text-blue-600 dark:text-blue-400 flex-shrink-0" />
                        <span className="text-gray-600 dark:text-gray-300">{neighborhood}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </section>

            {/* Climate Considerations */}
            <section>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                Charlotte Climate & Your Home's Exterior
              </h2>
              <Card className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
                <CardContent className="p-6">
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    Charlotte's humid subtropical climate presents unique challenges for exterior maintenance. Hot, humid 
                    summers and mild winters with occasional freezing create conditions that can accelerate wear on paint, 
                    wood, and other exterior surfaces.
                  </p>
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">Summer Challenges</h4>
                      <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                        <li>• High humidity and heat stress</li>
                        <li>• UV damage from intense sun</li>
                        <li>• Mold and mildew growth</li>
                        <li>• Frequent thunderstorms</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">Our Solutions</h4>
                      <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                        <li>• Climate-appropriate paint systems</li>
                        <li>• Mildew-resistant treatments</li>
                        <li>• UV-protective coatings</li>
                        <li>• Proper surface preparation</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </section>

            {/* Local Projects */}
            <section>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                Recent Charlotte Projects
              </h2>
              <div className="grid md:grid-cols-2 gap-6">
                <Card className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
                  <CardContent className="p-6">
                    <div className="aspect-video bg-gray-200 dark:bg-gray-700 rounded-lg mb-4 overflow-hidden">
                      <img 
                        src="https://images.pexels.com/photos/106399/pexels-photo-106399.jpeg?auto=compress&cs=tinysrgb&w=400"
                        alt="Charlotte home exterior painting project"
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">Myers Park Colonial Revival</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-300">Complete exterior restoration including wood repair, pressure washing, and premium paint application.</p>
                  </CardContent>
                </Card>
                <Card className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
                  <CardContent className="p-6">
                    <div className="aspect-video bg-gray-200 dark:bg-gray-700 rounded-lg mb-4 overflow-hidden">
                      <img 
                        src="https://images.pexels.com/photos/1396132/pexels-photo-1396132.jpeg?auto=compress&cs=tinysrgb&w=400"
                        alt="Charlotte deck restoration project"
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">Ballantyne Deck Restoration</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-300">Pressure washing, wood repair, and protective staining for a stunning outdoor living space.</p>
                  </CardContent>
                </Card>
              </div>
            </section>

            {/* Services Available */}
            <section>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                Our Charlotte Services
              </h2>
              <div className="grid md:grid-cols-2 gap-4">
                {services.map((service, index) => (
                  <Link key={index} href={service.link} 
                        className="block p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md transition-shadow">
                    <span className="text-blue-600 dark:text-blue-400 hover:underline font-medium">{service.name}</span>
                  </Link>
                ))}
              </div>
            </section>
          </div>

          {/* Sidebar */}
          <div className="space-y-8">
            {/* Contact Card */}
            <Card className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4 text-center">
                  Serving Charlotte
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <MapPin className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    <span className="text-gray-600 dark:text-gray-300">Charlotte Metro Area</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Phone className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    <span className="text-gray-600 dark:text-gray-300">(*************</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Clock className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    <span className="text-gray-600 dark:text-gray-300">Mon-Sat: 8AM-6PM</span>
                  </div>
                </div>
                <Link href="/contact" className="block mt-6">
                  <Button className="w-full bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600">
                    Get Free Charlotte Estimate
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Why Choose Us */}
            <Card className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
              <CardHeader>
                <CardTitle className="text-lg text-gray-900 dark:text-gray-100">
                  Why Charlotte Chooses Us
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Star className="h-5 w-5 text-yellow-500" />
                  <span className="text-gray-600 dark:text-gray-300">5-Star Local Reviews</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Clock className="h-5 w-5 text-green-600 dark:text-green-400" />
                  <span className="text-gray-600 dark:text-gray-300">Same-Day Estimates</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Home className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  <span className="text-gray-600 dark:text-gray-300">Local Charlotte Team</span>
                </div>
              </CardContent>
            </Card>

            {/* Nearby Locations */}
            <Card className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
              <CardHeader>
                <CardTitle className="text-lg text-gray-900 dark:text-gray-100">
                  Nearby Service Areas
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Link href="/locations/raleigh" className="block text-blue-600 dark:text-blue-400 hover:underline">
                  Raleigh
                </Link>
                <Link href="/locations/greensboro" className="block text-blue-600 dark:text-blue-400 hover:underline">
                  Greensboro
                </Link>
                <Link href="/locations/durham" className="block text-blue-600 dark:text-blue-400 hover:underline">
                  Durham
                </Link>
                <Link href="/locations" className="block text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400">
                  View All Locations →
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}