import './globals.css';
import type { Metadata } from 'next';
import { Layout } from '@/components/layout';

export const metadata: Metadata = {
  title: 'Graceful Shine - Professional Exterior Services',
  description: 'Expert painting, pressure washing, and wood repair services. Transform your home with Graceful Shine.',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="">
      <body className="font-sans">
        <Layout>
          {children}
        </Layout>
      </body>
    </html>
  );
}