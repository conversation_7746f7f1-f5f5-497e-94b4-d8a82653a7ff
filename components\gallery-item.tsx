import { Card } from '@/components/ui/card';

interface GalleryItemProps {
  beforeImage: string;
  afterImage: string;
  title: string;
}

export function GalleryItem({ beforeImage, afterImage, title }: GalleryItemProps) {
  return (
    <Card className="overflow-hidden hover:shadow-lg transition-shadow duration-200 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
      <div className="p-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">{title}</h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2 text-center">Before</p>
            <div className="aspect-square bg-gray-200 dark:bg-gray-700 rounded-md flex items-center justify-center">
              <img 
                src={beforeImage} 
                alt={`${title} - Before`}
                className="w-full h-full object-cover rounded-md"
              />
            </div>
          </div>
          <div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2 text-center">After</p>
            <div className="aspect-square bg-gray-200 dark:bg-gray-700 rounded-md flex items-center justify-center">
              <img 
                src={afterImage} 
                alt={`${title} - After`}
                className="w-full h-full object-cover rounded-md"
              />
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}