import Link from 'next/link';
import { ServiceDetailsCard } from '@/components/service-details-card';

export default function ServicesPage() {
  const services = [
    {
      title: "Interior Painting",
      description: "Transform your living spaces with our professional interior painting services. We use premium paints and meticulous techniques to deliver flawless results.",
      features: [
        "Color consultation and design advice",
        "Surface preparation and priming",
        "Premium paint application",
        "Clean-up and furniture protection",
        "Satisfaction guarantee"
      ]
    },
    {
      title: "Exterior Painting",
      description: "Protect and beautify your home's exterior with our weather-resistant painting solutions. Our expert team handles everything from prep to final coat.",
      features: [
        "Power washing and surface preparation",
        "Weather-resistant paint systems",
        "Trim and detail work",
        "Multi-coat application process",
        "Long-lasting finish guarantee"
      ]
    },
    {
      title: "Pressure Washing",
      description: "Restore your property's curb appeal with our professional pressure washing services. Safe for all surfaces, effective against dirt, grime, and stains.",
      features: [
        "Driveway and walkway cleaning",
        "Siding and exterior wall washing",
        "Deck and patio restoration",
        "Roof and gutter cleaning",
        "Eco-friendly cleaning solutions"
      ]
    },
    {
      title: "Wood Repair & Restoration",
      description: "Expert wood repair services to restore damaged siding, trim, and structural elements. We match existing materials and finishes perfectly.",
      features: [
        "Rot repair and replacement",
        "Trim and molding restoration",
        "Deck and fence repairs",
        "Custom millwork matching",
        "Weather sealing and protection"
      ]
    },
    {
      title: "Window Cleaning",
      description: "Crystal clear windows that let in maximum light and enhance your home's appearance. Regular maintenance keeps your windows spotless year-round.",
      features: [
        "Interior and exterior cleaning",
        "Screen cleaning and repair",
        "Sill and frame detailing",
        "Streak-free guarantee",
        "Seasonal maintenance plans"
      ]
    },
    {
      title: "Gutter Services",
      description: "Complete gutter maintenance to protect your home from water damage. From cleaning to repairs, we keep your gutters functioning perfectly.",
      isComingSoon: true,
      features: []
    }
  ];

  return (
    <div className="py-16">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            Our Services
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Comprehensive exterior services designed to enhance, protect, and maintain your property. 
            Each service is delivered with the highest standards of quality and attention to detail.
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 gap-8">
          {services.map((service, index) => (
            <ServiceDetailsCard key={index} {...service} />
          ))}
        </div>
      </div>
    </div>
  );
}